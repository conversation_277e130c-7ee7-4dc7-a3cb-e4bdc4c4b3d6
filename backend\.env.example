# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/order_management_db
MONGODB_USERNAME=admin
MONGODB_PASSWORD=your-mongodb-password-change-in-production
MONGODB_DATABASE=order_management_db

# Redis Configuration (for event store and caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password-change-in-production

# Security Keys
JWT_SECRET=your-super-secret-jwt-key-change-in-production-min-32-chars
JWT_EXPIRES_IN=7d
BCRYPT_SALT_ROUNDS=12
ENCRYPTION_KEY=your-32-char-encryption-key-change-in-production

# API Keys
API_KEY_SECRET=your-api-key-secret-change-in-production

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_SECRET=your-session-secret-change-in-production-min-32-chars

# Email Configuration (if needed)
EMAIL_SERVICE_API_KEY=your-email-service-api-key
EMAIL_FROM=<EMAIL>

# External Services (if needed)
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-webhook-secret-change-in-production

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log
