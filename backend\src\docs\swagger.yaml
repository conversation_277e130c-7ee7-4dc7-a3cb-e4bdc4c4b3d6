openapi: 3.0.3
info:
  title: Real-time Order Management System API
  description: |
    A comprehensive real-time order management system with payment processing, event-driven architecture, and microservice capabilities.
    
    ## Features
    - Complete order lifecycle management
    - Stripe payment processing integration
    - Real-time event system with WebSocket support
    - User authentication and authorization
    - Comprehensive validation and error handling
    
    ## Authentication
    Most endpoints require authentication via <PERSON><PERSON><PERSON> token in the Authorization header:
    ```
    Authorization: Bearer <your-jwt-token>
    ```
    
    ## Event System
    The system publishes events for all major operations, enabling real-time updates and integration with external systems.
    
  version: 1.0.0
  contact:
    name: Development Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:3001/api
    description: Development server
  - url: https://api.example.com/api
    description: Production server

tags:
  - name: Authentication
    description: User authentication and authorization
  - name: Users
    description: User management operations
  - name: Orders
    description: Order management and lifecycle
  - name: Payments
    description: Payment processing with Stripe integration
  - name: Events
    description: Event system monitoring and management
  - name: Health
    description: System health and monitoring

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for authentication

  schemas:
    Error:
      type: object
      properties:
        status:
          type: string
          enum: [fail, error]
        message:
          type: string
        errors:
          type: array
          items:
            type: object
      required:
        - status
        - message

    User:
      type: object
      properties:
        id:
          type: string
          description: Unique user identifier
        email:
          type: string
          format: email
        firstName:
          type: string
        lastName:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
      required:
        - id
        - email
        - firstName
        - lastName

    OrderItem:
      type: object
      properties:
        productId:
          type: string
        name:
          type: string
        quantity:
          type: integer
          minimum: 1
        unitPrice:
          type: number
          minimum: 0
        totalPrice:
          type: number
          minimum: 0
        sku:
          type: string
      required:
        - productId
        - name
        - quantity
        - unitPrice
        - sku

    Address:
      type: object
      properties:
        firstName:
          type: string
        lastName:
          type: string
        street:
          type: string
        city:
          type: string
        state:
          type: string
        zipCode:
          type: string
        country:
          type: string
          default: US
      required:
        - firstName
        - lastName
        - street
        - city
        - state
        - zipCode
        - country

    PaymentInfo:
      type: object
      properties:
        method:
          type: string
          enum: [stripe, paypal, cash_on_delivery]
        amount:
          type: number
          minimum: 0
        currency:
          type: string
          default: USD
        status:
          type: string
          enum: [pending, processing, completed, failed, cancelled]
        transactionId:
          type: string
        processedAt:
          type: string
          format: date-time
        failureReason:
          type: string
      required:
        - method
        - amount
        - currency

    Order:
      type: object
      properties:
        id:
          type: string
        orderNumber:
          type: string
        userId:
          type: string
        items:
          type: array
          items:
            $ref: '#/components/schemas/OrderItem'
        subtotal:
          type: number
          minimum: 0
        tax:
          type: number
          minimum: 0
        totalAmount:
          type: number
          minimum: 0
        status:
          type: string
          enum: [pending, confirmed, processing, shipped, delivered, cancelled, refunded]
        shippingAddress:
          $ref: '#/components/schemas/Address'
        billingAddress:
          $ref: '#/components/schemas/Address'
        payment:
          $ref: '#/components/schemas/PaymentInfo'
        notes:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
      required:
        - id
        - orderNumber
        - userId
        - items
        - totalAmount
        - status
        - shippingAddress
        - payment

    PaymentIntent:
      type: object
      properties:
        id:
          type: string
        amount:
          type: integer
          description: Amount in cents
        currency:
          type: string
        status:
          type: string
          enum: [requires_payment_method, requires_confirmation, requires_action, processing, requires_capture, canceled, succeeded]
        client_secret:
          type: string
        metadata:
          type: object
      required:
        - id
        - amount
        - currency
        - status

    Event:
      type: object
      properties:
        id:
          type: string
        eventType:
          type: string
        data:
          type: object
        metadata:
          type: object
          properties:
            correlationId:
              type: string
            causationId:
              type: string
            userId:
              type: string
            timestamp:
              type: string
              format: date-time
        createdAt:
          type: string
          format: date-time
      required:
        - id
        - eventType
        - data
        - metadata

    PaginationInfo:
      type: object
      properties:
        page:
          type: integer
          minimum: 1
        limit:
          type: integer
          minimum: 1
          maximum: 100
        total:
          type: integer
          minimum: 0
        pages:
          type: integer
          minimum: 0
      required:
        - page
        - limit
        - total
        - pages

security:
  - BearerAuth: []

paths:
  /health:
    get:
      tags:
        - Health
      summary: System health check
      description: Check the overall health status of the system
      security: []
      responses:
        '200':
          description: System is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: healthy
                  timestamp:
                    type: string
                    format: date-time
                  services:
                    type: object
                    properties:
                      database:
                        type: string
                        example: connected
                      redis:
                        type: string
                        example: connected
                      eventBus:
                        type: string
                        example: operational

  /users/register:
    post:
      tags:
        - Authentication
      summary: Register a new user
      description: Create a new user account
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  minLength: 6
                firstName:
                  type: string
                lastName:
                  type: string
              required:
                - email
                - password
                - firstName
                - lastName
      responses:
        '201':
          description: User registered successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/User'
                      token:
                        type: string
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '409':
          description: User already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /users/login:
    post:
      tags:
        - Authentication
      summary: User login
      description: Authenticate user and return JWT token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
              required:
                - email
                - password
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/User'
                      token:
                        type: string
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /orders:
    post:
      tags:
        - Orders
      summary: Create a new order
      description: Create a new order with items and shipping information
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/OrderItem'
                shippingAddress:
                  $ref: '#/components/schemas/Address'
                billingAddress:
                  $ref: '#/components/schemas/Address'
                payment:
                  $ref: '#/components/schemas/PaymentInfo'
                notes:
                  type: string
              required:
                - items
                - shippingAddress
                - payment
      responses:
        '201':
          description: Order created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      order:
                        $ref: '#/components/schemas/Order'
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /orders/my-orders:
    get:
      tags:
        - Orders
      summary: Get user's orders
      description: Retrieve orders for the authenticated user with pagination
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
        - name: status
          in: query
          schema:
            type: string
            enum: [pending, confirmed, processing, shipped, delivered, cancelled, refunded]
      responses:
        '200':
          description: Orders retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  results:
                    type: integer
                  pagination:
                    $ref: '#/components/schemas/PaginationInfo'
                  data:
                    type: object
                    properties:
                      orders:
                        type: array
                        items:
                          $ref: '#/components/schemas/Order'

  /orders/{id}:
    get:
      tags:
        - Orders
      summary: Get order by ID
      description: Retrieve a specific order by its ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Order retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    properties:
                      order:
                        $ref: '#/components/schemas/Order'
        '404':
          description: Order not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    patch:
      tags:
        - Orders
      summary: Update order
      description: Update order information
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  enum: [pending, confirmed, processing, shipped, delivered, cancelled, refunded]
                notes:
                  type: string
                shippingAddress:
                  $ref: '#/components/schemas/Address'
                payment:
                  $ref: '#/components/schemas/PaymentInfo'
      responses:
        '200':
          description: Order updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      order:
                        $ref: '#/components/schemas/Order'

    delete:
      tags:
        - Orders
      summary: Cancel order
      description: Cancel an order with optional reason
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
      responses:
        '200':
          description: Order cancelled successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string

  /payments/create-intent:
    post:
      tags:
        - Payments
      summary: Create payment intent
      description: Create a Stripe payment intent for processing payment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                amount:
                  type: integer
                  description: Amount in cents
                  minimum: 50
                currency:
                  type: string
                  default: usd
                orderId:
                  type: string
                metadata:
                  type: object
              required:
                - amount
      responses:
        '201':
          description: Payment intent created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      paymentIntent:
                        $ref: '#/components/schemas/PaymentIntent'
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /payments/confirm:
    post:
      tags:
        - Payments
      summary: Confirm payment
      description: Confirm a payment intent with payment method
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                paymentIntentId:
                  type: string
                paymentMethodId:
                  type: string
              required:
                - paymentIntentId
                - paymentMethodId
      responses:
        '200':
          description: Payment confirmed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      paymentIntent:
                        $ref: '#/components/schemas/PaymentIntent'

  /payments/webhook:
    post:
      tags:
        - Payments
      summary: Stripe webhook endpoint
      description: Handle Stripe webhook events for payment processing
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
      responses:
        '200':
          description: Webhook processed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  received:
                    type: boolean
                    example: true
        '400':
          description: Webhook verification failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string

  /events:
    get:
      tags:
        - Events
      summary: Get events
      description: Retrieve system events with pagination and filtering
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
        - name: eventType
          in: query
          schema:
            type: string
        - name: userId
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Events retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  results:
                    type: integer
                  pagination:
                    $ref: '#/components/schemas/PaginationInfo'
                  data:
                    type: object
                    properties:
                      events:
                        type: array
                        items:
                          $ref: '#/components/schemas/Event'

  /events/health:
    get:
      tags:
        - Events
      summary: Event system health check
      description: Check the health status of the event system
      responses:
        '200':
          description: Event system is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: healthy
                  eventBus:
                    type: string
                    example: operational
                  eventStore:
                    type: string
                    example: connected
